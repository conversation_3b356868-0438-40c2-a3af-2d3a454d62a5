package cmd

import (
	"fmt"
	"os"

	"github.com/liyujun-dev/kui/internal/app"
	"github.com/spf13/cobra"
)

var (
	version   = "dev"
	commit    = "unknown"
	buildDate = "unknown"
)

var rootCmd = &cobra.Command{
	Use:   "kui",
	Short: "🌻 A TUI for managing Kubernetes resources",
	Long: `K<PERSON> (葵) - A beautiful terminal user interface for managing Kubernetes resources.

KUI provides an intuitive and efficient way to view and manage your Kubernetes
clusters through a modern terminal interface. Named after the sunflower (葵),
it aims to bring clarity and brightness to your Kubernetes workflow.`,
	Version: fmt.Sprintf("%s (%s, %s)", version, commit, buildDate),
	RunE: func(cmd *cobra.Command, args []string) error {
		// If no subcommands are provided, start the TUI
		return app.Run()
	},
}

func Execute() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
