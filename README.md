# 葵 (<PERSON><PERSON>) 🌻

A beautiful terminal user interface for managing Kubernetes resources.

![K<PERSON> Demo](docs/demo.png)

## Overview

KUI (葵, pronounced "kuí") is a modern terminal user interface (TUI) application for managing Kubernetes resources. Named after the sunflower, KUI aims to bring clarity and brightness to your Kubernetes workflow with an intuitive and efficient interface.

## Features

- 🌻 **Beautiful Interface**: Modern TUI with Catppuccin Latte color scheme
- ⚡ **Fast Navigation**: Vim-style keyboard shortcuts for efficient navigation
- 📊 **Resource Overview**: View Pods, Deployments, Services, and more in organized tables
- 🎯 **Focus Management**: Clear visual indicators for focused panels and selected items
- 📱 **Responsive Layout**: Adapts to different terminal sizes
- ⚙️ **Configurable**: Customizable themes and layout settings

## Installation

### From Source

```bash
# Clone the repository
git clone https://github.com/liyujun-dev/kui.git
cd kui

# Build and install
make install
```

### Using Go

```bash
go install github.com/liyujun-dev/kui@latest
```

## Usage

### Basic Usage

Start KUI by simply running:

```bash
kui
```

### Keyboard Shortcuts

| Key | Action |
|-----|--------|
| `j` / `↓` | Move down |
| `k` / `↑` | Move up |
| `h` / `←` | Move left |
| `l` / `→` | Move right |
| `Tab` | Switch between panels |
| `Enter` | Select/Enter |
| `r` | Refresh current view |
| `q` / `Ctrl+C` | Quit |

### Command Line Options

```bash
kui --help          # Show help
kui --version       # Show version information
```
