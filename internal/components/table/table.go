package table

import (
	"fmt"
	"strings"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/liyujun-dev/kui/internal/models"
)

// Column represents a table column
type Column struct {
	Title string
	Width int
}

// Table represents a data table component
type Table struct {
	width       int
	height      int
	columns     []Column
	rows        []models.Resource
	selectedRow int
	focused     bool
	namespace   string
	loading     bool
	error       string
}

// New creates a new table component
func New(width, height int) *Table {
	columns := []Column{
		{Title: "Name", Width: 16},
		{Title: "Containers", Width: 10},
		{Title: "CPU", Width: 5},
		{Title: "Mem", Width: 9},
		{Title: "Restarts", Width: 8},
		{Title: "Node", Width: 6},
		{Title: "Age", Width: 3},
		{Title: "Status", Width: 8},
	}

	return &Table{
		width:       width,
		height:      height,
		columns:     columns,
		rows:        []models.Resource{}, // Start with empty data
		selectedRow: 0,
		focused:     false,
		namespace:   "default",
		loading:     false,
		error:       "",
	}
}

// Init initializes the table component
func (t *Table) Init() tea.Cmd {
	return nil
}

// Update handles messages for the table
func (t *Table) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	if !t.focused {
		return t, nil
	}

	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "j", "down":
			if t.selectedRow < len(t.rows)-1 {
				t.selectedRow++
			}
		case "k", "up":
			if t.selectedRow > 0 {
				t.selectedRow--
			}
		case "g":
			t.selectedRow = 0
		case "G":
			t.selectedRow = len(t.rows) - 1
		}
	}

	return t, nil
}

// View renders the table
func (t *Table) View() string {
	// Create border with namespace title
	border := t.createBorder()
	
	// Create table content
	content := t.renderTable()
	
	// Apply styling
	style := lipgloss.NewStyle().
		Border(border).
		Width(t.width).
		Height(t.height).
		Padding(0, 1)

	if t.focused {
		style = style.BorderForeground(lipgloss.Color("#1e66f5")) // Catppuccin Blue
	} else {
		style = style.BorderForeground(lipgloss.Color("#9ca0b0")) // Catppuccin Surface2
	}

	return style.Render(content)
}

// createBorder creates a custom border with namespace title and count footer
func (t *Table) createBorder() lipgloss.Border {
	border := lipgloss.RoundedBorder()
	
	// Create title with namespace
	title := fmt.Sprintf("Namespace: %s ▼", t.namespace)
	titleLen := len(title)
	remainingWidth := t.width - titleLen - 4
	
	if remainingWidth > 0 {
		border.Top = title + " " + strings.Repeat("─", remainingWidth)
	} else {
		border.Top = title
	}
	
	// Create footer with count
	footer := fmt.Sprintf("Count: %d", len(t.rows))
	footerLen := len(footer)
	remainingFooterWidth := t.width - footerLen - 4
	
	if remainingFooterWidth > 0 {
		border.Bottom = strings.Repeat("─", remainingFooterWidth) + " " + footer + " ─"
	} else {
		border.Bottom = footer
	}
	
	return border
}

// renderTable renders the table content
func (t *Table) renderTable() string {
	if t.loading {
		return "Loading resources..."
	}
	
	if t.error != "" {
		return fmt.Sprintf("Error: %s", t.error)
	}
	
	if len(t.rows) == 0 {
		return "No resources found"
	}

	var content strings.Builder
	
	// Render header
	header := t.renderHeader()
	content.WriteString(header)
	content.WriteString("\n")
	
	// Render rows
	for i, row := range t.rows {
		rowStr := t.renderRow(row, i == t.selectedRow)
		content.WriteString(rowStr)
		if i < len(t.rows)-1 {
			content.WriteString("\n")
		}
	}
	
	return content.String()
}

// renderHeader renders the table header
func (t *Table) renderHeader() string {
	var header strings.Builder
	
	for i, col := range t.columns {
		cellContent := t.truncateString(col.Title, col.Width)
		cellContent = t.padString(cellContent, col.Width)
		
		// Style header
		styledCell := lipgloss.NewStyle().
			Foreground(lipgloss.Color("#4c4f69")). // Catppuccin Text
			Bold(true).
			Render(cellContent)
		
		header.WriteString(styledCell)
		
		if i < len(t.columns)-1 {
			header.WriteString(" ")
		}
	}
	
	return header.String()
}

// renderRow renders a single table row
func (t *Table) renderRow(resource models.Resource, selected bool) string {
	var row strings.Builder
	
	// Prepare row data
	rowData := []string{
		resource.Name,
		resource.Containers,
		resource.CPU,
		resource.Memory,
		fmt.Sprintf("%d", resource.Restarts),
		resource.Node,
		resource.FormatAge(),
		resource.Status,
	}
	
	for i, data := range rowData {
		if i >= len(t.columns) {
			break
		}
		
		cellContent := t.truncateString(data, t.columns[i].Width)
		cellContent = t.padString(cellContent, t.columns[i].Width)
		
		// Style cell based on selection and status
		var styledCell string
		if selected {
			styledCell = lipgloss.NewStyle().
				Foreground(lipgloss.Color("#eff1f5")). // Catppuccin Base
				Background(lipgloss.Color("#1e66f5")). // Catppuccin Blue
				Render(cellContent)
		} else {
			color := "#4c4f69" // Catppuccin Text
			if i == len(rowData)-1 { // Status column
				switch resource.Status {
				case "Running":
					color = "#40a02b" // Catppuccin Green
				case "Stopped":
					color = "#d20f39" // Catppuccin Red
				case "Pending":
					color = "#df8e1d" // Catppuccin Yellow
				}
			}
			
			styledCell = lipgloss.NewStyle().
				Foreground(lipgloss.Color(color)).
				Render(cellContent)
		}
		
		row.WriteString(styledCell)
		
		if i < len(t.columns)-1 {
			row.WriteString(" ")
		}
	}
	
	return row.String()
}

// truncateString truncates a string to fit within the specified width
func (t *Table) truncateString(s string, width int) string {
	if len(s) <= width {
		return s
	}
	if width <= 3 {
		return s[:width]
	}
	return s[:width-3] + "..."
}

// padString pads a string to the specified width
func (t *Table) padString(s string, width int) string {
	if len(s) >= width {
		return s
	}
	return s + strings.Repeat(" ", width-len(s))
}

// SetFocused sets the focus state of the table
func (t *Table) SetFocused(focused bool) {
	t.focused = focused
}

// IsFocused returns whether the table is focused
func (t *Table) IsFocused() bool {
	return t.focused
}

// SetSize updates the table dimensions
func (t *Table) SetSize(width, height int) {
	t.width = width
	t.height = height
}

// SetNamespace sets the current namespace
func (t *Table) SetNamespace(namespace string) {
	t.namespace = namespace
}

// SetData sets the table data
func (t *Table) SetData(resources []models.Resource) {
	t.rows = resources
	t.selectedRow = 0
	t.loading = false
	t.error = ""
}

// SetLoading sets the loading state
func (t *Table) SetLoading(loading bool) {
	t.loading = loading
	if loading {
		t.error = ""
	}
}

// SetError sets the error state
func (t *Table) SetError(err string) {
	t.error = err
	t.loading = false
}

// GetSelectedResource returns the currently selected resource
func (t *Table) GetSelectedResource() *models.Resource {
	if t.selectedRow >= 0 && t.selectedRow < len(t.rows) {
		return &t.rows[t.selectedRow]
	}
	return nil
}
