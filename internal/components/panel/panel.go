package panel

import (
	"strings"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/liyujun-dev/kui/internal/components/dropdown"
	"github.com/liyujun-dev/kui/internal/styles"
)

type Panel struct {
	width    int
	height   int
	content  string
	dropdown *dropdown.Dropdown
	footer   string
	Focused  bool
}

func New(width, height int, content, footer string, dropdown *dropdown.Dropdown) *Panel {
	return &Panel{
		width:    width,
		height:   height,
		content:  content,
		footer:   footer,
		dropdown: dropdown,
		Focused:  false,
	}
}

func (p *Panel) Init() tea.Cmd {
	return nil
}

func (p *Panel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+t":
			p.dropdown.Focused = true
			return p, nil
		}
	}
	if p.dropdown.Focused {
		_, cmd := p.dropdown.Update(msg)
		return p, cmd
	}
	return p, nil
}

func (p *Panel) View() string {
	border := p.renderBorder()
	style := lipgloss.NewStyle().Border(border).Width(p.width).Height(p.height)
	if p.dropdown != nil && p.dropdown.Focused {
		return style.PaddingLeft(2).Render(p.dropdown.View())
	}
	return style.Render(p.content)
}

func (p *Panel) renderBorder() lipgloss.Border {
	border := styles.Border
	if p.dropdown != nil {
		border.Top = border.Top + " " + p.dropdown.GetSelected() + " ▼ " + strings.Repeat(border.Top, p.width)
	}

	if p.footer != "" {
		actualWidth := p.width - len(p.footer) - 3
		border.Bottom = strings.Repeat(border.Bottom, actualWidth) + " " + p.footer + " " + border.Bottom
	}
	return border
}

// SetSize updates the panel dimensions
func (p *Panel) SetSize(width, height int) {
	p.width = width
	p.height = height
}
