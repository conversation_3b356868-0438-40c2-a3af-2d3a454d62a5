package dropdown

import (
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// Dropdown is a component that allows the user to select an option from a list
type Dropdown struct {
	currentIndex int
	Options      []string
	Focused      bool
}

func New(options []string) *Dropdown {
	return &Dropdown{
		currentIndex: 0,
		Options:      options,
		Focused:      false,
	}
}

func (d *Dropdown) Init() tea.Cmd {
	return nil
}

func (d *Dropdown) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "k", "up":
			d.currentIndex--
			if d.currentIndex < 0 {
				d.currentIndex = len(d.Options) - 1
			}
		case "j", "down":
			d.currentIndex++
			if d.currentIndex >= len(d.Options) {
				d.currentIndex = 0
			}
		case "enter":
			d.Focused = !d.Focused
		case "esc":
			d.Focused = false
		}
	}
	return d, nil
}

func (d *Dropdown) View() string {
	if !d.Focused {
		return d.GetSelected()
	}

	content := ""
	for i, line := range d.Options {
		if i == d.currentIndex {
			content += lipgloss.NewStyle().Foreground(lipgloss.Color("#1e66f5")).Render(line)
		} else {
			content += lipgloss.NewStyle().Foreground(lipgloss.Color("#4c4f69")).Render(line)
		}
		content += "\n"
	}
	return content
}

func (d *Dropdown) GetSelected() string {
	return d.Options[d.currentIndex]
}
