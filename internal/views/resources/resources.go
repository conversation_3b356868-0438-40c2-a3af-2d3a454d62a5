package resources

import (
	"context"
	"time"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/liyujun-dev/kui/internal/components/dropdown"
	"github.com/liyujun-dev/kui/internal/components/panel"
	"github.com/liyujun-dev/kui/internal/components/table"
	"github.com/liyujun-dev/kui/internal/k8s"
	"github.com/liyujun-dev/kui/internal/models"
)

// FocusedComponent represents which component is currently focused
type FocusedComponent int

const (
	FocusedSidebar FocusedComponent = iota
	FocusedTable
)

// ResourcesLoadedMsg is sent when resources are loaded
type ResourcesLoadedMsg struct {
	Resources []models.Resource
	Error     error
}

type Resources struct {
	sidebar          *panel.Panel
	table            *table.Table
	focusedComponent FocusedComponent
	width            int
	height           int
	k8sClient        *k8s.Client
	resourceService  *k8s.ResourceService
	currentNamespace string
	currentResource  models.ResourceType
}

func New() *Resources {
	// Initialize K8s client
	client, err := k8s.NewClient()
	var resourceService *k8s.ResourceService
	if err == nil {
		resourceService = k8s.NewResourceService(client)
	}

	// Create sidebar with resource types
	resourceTypes := models.GetAllResourceTypes()
	options := make([]string, len(resourceTypes))
	for i, rt := range resourceTypes {
		options[i] = string(rt)
	}

	// Create dropdown for resource types
	resourceDropdown := dropdown.New(options)

	return &Resources{
		sidebar:          panel.New(20, 10, "", "", resourceDropdown), // Use Panel for sidebar
		table:            table.New(60, 10),                           // Default dimensions, will be updated
		focusedComponent: FocusedSidebar,                              // Start with sidebar focused
		k8sClient:        client,
		resourceService:  resourceService,
		currentNamespace: "default",
		currentResource:  models.ResourceTypePods,
	}
}

func (r *Resources) Init() tea.Cmd {
	var cmds []tea.Cmd

	if cmd := r.sidebar.Init(); cmd != nil {
		cmds = append(cmds, cmd)
	}

	if cmd := r.table.Init(); cmd != nil {
		cmds = append(cmds, cmd)
	}

	// Load initial data
	if r.resourceService != nil {
		cmds = append(cmds, r.loadResources())
	}

	return tea.Batch(cmds...)
}

// loadResources loads resources from Kubernetes
func (r *Resources) loadResources() tea.Cmd {
	return func() tea.Msg {
		if r.resourceService == nil {
			return ResourcesLoadedMsg{
				Resources: models.GetMockResources(), // Fallback to mock data
				Error:     nil,
			}
		}

		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		resources, err := r.resourceService.GetResourcesByType(ctx, r.currentNamespace, r.currentResource)
		return ResourcesLoadedMsg{
			Resources: resources,
			Error:     err,
		}
	}
}

func (r *Resources) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case ResourcesLoadedMsg:
		if msg.Error != nil {
			r.table.SetError(msg.Error.Error())
		} else {
			r.table.SetData(msg.Resources)
		}
		return r, nil
	case tea.KeyMsg:
		switch msg.String() {
		case "tab":
			// Switch focus between components
			r.switchFocus()
			return r, nil
		case "enter":
			// Handle enter key based on focused component
			if r.focusedComponent == FocusedSidebar {
				// Toggle dropdown or handle selection
				return r, nil
			}
		case "r":
			// Refresh data
			if r.resourceService != nil {
				r.table.SetLoading(true)
				return r, r.loadResources()
			}
		}
	case tea.WindowSizeMsg:
		r.width = msg.Width
		r.height = msg.Height
		r.updateComponentSizes()
	}

	// Update focused component
	switch r.focusedComponent {
	case FocusedSidebar:
		var cmd tea.Cmd
		_, cmd = r.sidebar.Update(msg)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}
	case FocusedTable:
		var cmd tea.Cmd
		_, cmd = r.table.Update(msg)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}
	}

	return r, tea.Batch(cmds...)
}

func (r *Resources) View() string {
	// Update focus states
	r.sidebar.Focused = (r.focusedComponent == FocusedSidebar)
	r.table.SetFocused(r.focusedComponent == FocusedTable)

	// Get views from components
	sidebarView := r.sidebar.View()
	tableView := r.table.View()

	// Use lipgloss to create horizontal layout
	return lipgloss.JoinHorizontal(lipgloss.Top, sidebarView, tableView)
}

// switchFocus switches focus between sidebar and table
func (r *Resources) switchFocus() {
	switch r.focusedComponent {
	case FocusedSidebar:
		r.focusedComponent = FocusedTable
	case FocusedTable:
		r.focusedComponent = FocusedSidebar
	}
}

// updateComponentSizes updates the sizes of child components
func (r *Resources) updateComponentSizes() {
	if r.width <= 0 || r.height <= 0 {
		return
	}

	// Calculate dimensions (similar to layout logic)
	sidebarWidth := r.width / 4
	if sidebarWidth < 20 {
		sidebarWidth = 20
	}
	if sidebarWidth > r.width/2 {
		sidebarWidth = r.width / 2
	}

	tableWidth := r.width - sidebarWidth
	componentHeight := r.height // Full height for now

	r.sidebar.SetSize(sidebarWidth, componentHeight)
	r.table.SetSize(tableWidth, componentHeight)
}

// GetSelectedResource returns the currently selected resource type from sidebar
func (r *Resources) GetSelectedResource() models.ResourceType {
	return r.currentResource
}
