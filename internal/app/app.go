package app

import (
	tea "github.com/charmbracelet/bubbletea"
	"github.com/liyujun-dev/kui/internal/views"
	"github.com/liyujun-dev/kui/internal/views/resources"
)

type App struct {
	currentView views.View
}

func New() (*App, error) {
	resourcesView := resources.New()
	return &App{
		currentView: resourcesView,
	}, nil
}

func (a *App) Init() tea.Cmd {
	return nil
}

func (a *App) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "q", "ctrl+c":
			return a, tea.Quit
		}
	}

	return a, nil
}

func (a *App) View() string {
	return ""
}

// Run starts the TUI application
func Run() error {
	app, err := New()
	if err != nil {
		return err
	}

	// Create and run the Bubble Tea program
	p := tea.NewProgram(app, tea.WithAltScreen(), tea.WithMouseCellMotion())
	_, err = p.Run()
	return err
}
