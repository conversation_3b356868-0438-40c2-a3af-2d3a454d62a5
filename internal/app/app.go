package app

import (
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/liyujun-dev/kui/internal/components/statusbar"
	"github.com/liyujun-dev/kui/internal/views"
	"github.com/liyujun-dev/kui/internal/views/resources"
)

type App struct {
	currentView views.View
	statusbar   *statusbar.Statusbar
	width       int
	height      int
}

func New() (*App, error) {
	resourcesView := resources.New()
	statusBar := statusbar.New(" orbstack v1.32.1", "🌻 ? help") // Default width and version

	return &App{
		currentView: resourcesView,
		statusbar:   statusBar,
		width:       80,
		height:      24,
	}, nil
}

func (a *App) Init() tea.Cmd {
	return a.currentView.Init()
}

func (a *App) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "q", "ctrl+c":
			return a, tea.Quit
		}
	case tea.WindowSizeMsg:
		a.width = msg.Width
		a.height = msg.Height
	}

	// Update current view
	var cmd tea.Cmd
	a.currentView, cmd = a.currentView.Update(msg)
	if cmd != nil {
		cmds = append(cmds, cmd)
	}

	return a, tea.Batch(cmds...)
}

func (a *App) View() string {
	if a.width <= 0 || a.height <= 0 {
		return "Loading..."
	}

	// Get component views
	mainView := a.currentView.View()
	statusView := a.statusbar.View()

	// Use layout to combine views (main content on top, status bar at bottom)
	return lipgloss.JoinVertical(lipgloss.Left, mainView, statusView)
}

// Run starts the TUI application
func Run() error {
	app, err := New()
	if err != nil {
		return err
	}

	// Create and run the Bubble Tea program
	p := tea.NewProgram(app, tea.WithAltScreen(), tea.WithMouseCellMotion())
	_, err = p.Run()
	return err
}
