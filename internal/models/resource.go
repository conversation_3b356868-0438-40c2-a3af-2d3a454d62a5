package models

import (
	"fmt"
	"time"
)

// ResourceType represents different types of Kubernetes resources
type ResourceType string

const (
	ResourceTypePods         ResourceType = "Pods"
	ResourceTypeDeployments  ResourceType = "Deployments"
	ResourceTypeServices     ResourceType = "Services"
	ResourceTypeDaemonSets   ResourceType = "Daemon Sets"
	ResourceTypeStatefulSets ResourceType = "Stateful Sets"
	ResourceTypeReplicaSets  ResourceType = "Replica Sets"
	ResourceTypeJobs         ResourceType = "Jobs"
	ResourceTypeCronJobs     ResourceType = "Cron Jobs"
)

// GetAllResourceTypes returns all available resource types
func GetAllResourceTypes() []ResourceType {
	return []ResourceType{
		ResourceTypePods,
		ResourceTypeDeployments,
		ResourceTypeServices,
		ResourceTypeDaemonSets,
		ResourceTypeStatefulSets,
		ResourceTypeReplicaSets,
		ResourceTypeJobs,
		ResourceTypeCronJobs,
	}
}

// Resource represents a Kubernetes resource with display information
type Resource struct {
	Name       string
	Containers string
	CPU        string
	Memory     string
	Restarts   int
	Node       string
	Age        time.Duration
	Status     string
}

// ResourceStatus represents the status of a resource
type ResourceStatus string

const (
	StatusRunning ResourceStatus = "Running"
	StatusStopped ResourceStatus = "Stopped"
	StatusPending ResourceStatus = "Pending"
	StatusFailed  ResourceStatus = "Failed"
)

// ConnectionStatus represents the connection status to Kubernetes
type ConnectionStatus string

const (
	ConnectionStatusConnected    ConnectionStatus = "Connected"
	ConnectionStatusDisconnected ConnectionStatus = "Disconnected"
	ConnectionStatusConnecting   ConnectionStatus = "Connecting"
	ConnectionStatusError        ConnectionStatus = "Error"
)

// ResourceError represents an error when fetching resources
type ResourceError struct {
	Type    ResourceType
	Message string
	Err     error
}

func (re *ResourceError) Error() string {
	return fmt.Sprintf("failed to fetch %s: %s", re.Type, re.Message)
}

// FormatAge formats the age duration for display
func (r *Resource) FormatAge() string {
	if r.Age < time.Minute {
		return "1s"
	} else if r.Age < time.Hour {
		return "1m"
	} else if r.Age < 24*time.Hour {
		return "2h"
	} else if r.Age < 365*24*time.Hour {
		return "1y"
	}
	return "1y+"
}

// GetMockResources returns mock data for testing (deprecated - use real K8s data)
func GetMockResources() []Resource {
	return []Resource{
		{
			Name:       "nginx-jkjfsass",
			Containers: "2/2",
			CPU:        "0.1%",
			Memory:     "2.2KiB",
			Restarts:   0,
			Node:       "node1",
			Age:        time.Second,
			Status:     string(StatusRunning),
		},
		{
			Name:       "alpine-sfjkj12",
			Containers: "1/1",
			CPU:        "0.2%",
			Memory:     "5.1MiB",
			Restarts:   2,
			Node:       "node1",
			Age:        365 * 24 * time.Hour,
			Status:     string(StatusRunning),
		},
		{
			Name:       "ubuntu-123kjsd",
			Containers: "1/1",
			CPU:        "5.0%",
			Memory:     "1.1GiB",
			Restarts:   0,
			Node:       "node1",
			Age:        time.Minute,
			Status:     string(StatusStopped),
		},
		{
			Name:       "java-jkjfsass",
			Containers: "1/1",
			CPU:        "3.0%",
			Memory:     "202.2MiB",
			Restarts:   0,
			Node:       "node2",
			Age:        2 * time.Hour,
			Status:     string(StatusRunning),
		},
	}
}
