package k8s

import (
	"context"
	"fmt"
	"time"

	appsv1 "k8s.io/api/apps/v1"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/liyujun-dev/kui/internal/models"
)

// ResourceService provides methods to fetch Kubernetes resources
type ResourceService struct {
	client *Client
}

// NewResourceService creates a new resource service
func NewResourceService(client *Client) *ResourceService {
	return &ResourceService{
		client: client,
	}
}

// GetPods returns a list of pods in the specified namespace
func (rs *ResourceService) GetPods(ctx context.Context, namespace string) ([]models.Resource, error) {
	pods, err := rs.client.clientset.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list pods: %w", err)
	}

	var resources []models.Resource
	for _, pod := range pods.Items {
		resource := rs.convertPodToResource(pod)
		resources = append(resources, resource)
	}

	return resources, nil
}

// GetDeployments returns a list of deployments in the specified namespace
func (rs *ResourceService) GetDeployments(ctx context.Context, namespace string) ([]models.Resource, error) {
	deployments, err := rs.client.clientset.AppsV1().Deployments(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list deployments: %w", err)
	}

	var resources []models.Resource
	for _, deployment := range deployments.Items {
		resource := rs.convertDeploymentToResource(deployment)
		resources = append(resources, resource)
	}

	return resources, nil
}

// GetServices returns a list of services in the specified namespace
func (rs *ResourceService) GetServices(ctx context.Context, namespace string) ([]models.Resource, error) {
	services, err := rs.client.clientset.CoreV1().Services(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list services: %w", err)
	}

	var resources []models.Resource
	for _, service := range services.Items {
		resource := rs.convertServiceToResource(service)
		resources = append(resources, resource)
	}

	return resources, nil
}

// GetDaemonSets returns a list of daemon sets in the specified namespace
func (rs *ResourceService) GetDaemonSets(ctx context.Context, namespace string) ([]models.Resource, error) {
	daemonSets, err := rs.client.clientset.AppsV1().DaemonSets(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list daemon sets: %w", err)
	}

	var resources []models.Resource
	for _, ds := range daemonSets.Items {
		resource := rs.convertDaemonSetToResource(ds)
		resources = append(resources, resource)
	}

	return resources, nil
}

// GetStatefulSets returns a list of stateful sets in the specified namespace
func (rs *ResourceService) GetStatefulSets(ctx context.Context, namespace string) ([]models.Resource, error) {
	statefulSets, err := rs.client.clientset.AppsV1().StatefulSets(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list stateful sets: %w", err)
	}

	var resources []models.Resource
	for _, ss := range statefulSets.Items {
		resource := rs.convertStatefulSetToResource(ss)
		resources = append(resources, resource)
	}

	return resources, nil
}

// GetReplicaSets returns a list of replica sets in the specified namespace
func (rs *ResourceService) GetReplicaSets(ctx context.Context, namespace string) ([]models.Resource, error) {
	replicaSets, err := rs.client.clientset.AppsV1().ReplicaSets(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list replica sets: %w", err)
	}

	var resources []models.Resource
	for _, replicaSet := range replicaSets.Items {
		resource := rs.convertReplicaSetToResource(replicaSet)
		resources = append(resources, resource)
	}

	return resources, nil
}

// GetJobs returns a list of jobs in the specified namespace
func (rs *ResourceService) GetJobs(ctx context.Context, namespace string) ([]models.Resource, error) {
	jobs, err := rs.client.clientset.BatchV1().Jobs(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list jobs: %w", err)
	}

	var resources []models.Resource
	for _, job := range jobs.Items {
		resource := rs.convertJobToResource(job)
		resources = append(resources, resource)
	}

	return resources, nil
}

// GetCronJobs returns a list of cron jobs in the specified namespace
func (rs *ResourceService) GetCronJobs(ctx context.Context, namespace string) ([]models.Resource, error) {
	cronJobs, err := rs.client.clientset.BatchV1().CronJobs(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list cron jobs: %w", err)
	}

	var resources []models.Resource
	for _, cronJob := range cronJobs.Items {
		resource := rs.convertCronJobToResource(cronJob)
		resources = append(resources, resource)
	}

	return resources, nil
}

// GetResourcesByType returns resources based on the specified type
func (rs *ResourceService) GetResourcesByType(ctx context.Context, namespace string, resourceType models.ResourceType) ([]models.Resource, error) {
	switch resourceType {
	case models.ResourceTypePods:
		return rs.GetPods(ctx, namespace)
	case models.ResourceTypeDeployments:
		return rs.GetDeployments(ctx, namespace)
	case models.ResourceTypeServices:
		return rs.GetServices(ctx, namespace)
	case models.ResourceTypeDaemonSets:
		return rs.GetDaemonSets(ctx, namespace)
	case models.ResourceTypeStatefulSets:
		return rs.GetStatefulSets(ctx, namespace)
	case models.ResourceTypeReplicaSets:
		return rs.GetReplicaSets(ctx, namespace)
	case models.ResourceTypeJobs:
		return rs.GetJobs(ctx, namespace)
	case models.ResourceTypeCronJobs:
		return rs.GetCronJobs(ctx, namespace)
	default:
		return nil, fmt.Errorf("unsupported resource type: %s", resourceType)
	}
}

// convertPodToResource converts a Kubernetes Pod to our Resource model
func (rs *ResourceService) convertPodToResource(pod corev1.Pod) models.Resource {
	// Calculate containers status
	totalContainers := len(pod.Spec.Containers)
	readyContainers := 0
	for _, status := range pod.Status.ContainerStatuses {
		if status.Ready {
			readyContainers++
		}
	}

	// Calculate resource usage (simplified)
	cpu := "0m"
	memory := "0Mi"

	// Get restart count
	restarts := int32(0)
	for _, status := range pod.Status.ContainerStatuses {
		restarts += status.RestartCount
	}

	// Calculate age
	age := time.Since(pod.CreationTimestamp.Time)

	// Determine status
	status := string(pod.Status.Phase)
	if pod.DeletionTimestamp != nil {
		status = "Terminating"
	}

	return models.Resource{
		Name:       pod.Name,
		Containers: fmt.Sprintf("%d/%d", readyContainers, totalContainers),
		CPU:        cpu,
		Memory:     memory,
		Restarts:   int(restarts),
		Node:       pod.Spec.NodeName,
		Age:        age,
		Status:     status,
	}
}

// convertDeploymentToResource converts a Kubernetes Deployment to our Resource model
func (rs *ResourceService) convertDeploymentToResource(deployment appsv1.Deployment) models.Resource {
	replicas := int32(0)
	if deployment.Spec.Replicas != nil {
		replicas = *deployment.Spec.Replicas
	}

	age := time.Since(deployment.CreationTimestamp.Time)

	status := "Running"
	if deployment.Status.ReadyReplicas != replicas {
		status = "Pending"
	}

	return models.Resource{
		Name:       deployment.Name,
		Containers: fmt.Sprintf("%d/%d", deployment.Status.ReadyReplicas, replicas),
		CPU:        "0m",
		Memory:     "0Mi",
		Restarts:   0,
		Node:       "-",
		Age:        age,
		Status:     status,
	}
}

// convertServiceToResource converts a Kubernetes Service to our Resource model
func (rs *ResourceService) convertServiceToResource(service corev1.Service) models.Resource {
	age := time.Since(service.CreationTimestamp.Time)

	clusterIP := service.Spec.ClusterIP
	if clusterIP == "" {
		clusterIP = "None"
	}

	return models.Resource{
		Name:       service.Name,
		Containers: clusterIP,
		CPU:        "-",
		Memory:     "-",
		Restarts:   0,
		Node:       "-",
		Age:        age,
		Status:     "Active",
	}
}

// Additional conversion methods for other resource types...
func (rs *ResourceService) convertDaemonSetToResource(ds appsv1.DaemonSet) models.Resource {
	age := time.Since(ds.CreationTimestamp.Time)

	status := "Running"
	if ds.Status.NumberReady != ds.Status.DesiredNumberScheduled {
		status = "Pending"
	}

	return models.Resource{
		Name:       ds.Name,
		Containers: fmt.Sprintf("%d/%d", ds.Status.NumberReady, ds.Status.DesiredNumberScheduled),
		CPU:        "0m",
		Memory:     "0Mi",
		Restarts:   0,
		Node:       "-",
		Age:        age,
		Status:     status,
	}
}

func (rs *ResourceService) convertStatefulSetToResource(ss appsv1.StatefulSet) models.Resource {
	replicas := int32(0)
	if ss.Spec.Replicas != nil {
		replicas = *ss.Spec.Replicas
	}

	age := time.Since(ss.CreationTimestamp.Time)

	status := "Running"
	if ss.Status.ReadyReplicas != replicas {
		status = "Pending"
	}

	return models.Resource{
		Name:       ss.Name,
		Containers: fmt.Sprintf("%d/%d", ss.Status.ReadyReplicas, replicas),
		CPU:        "0m",
		Memory:     "0Mi",
		Restarts:   0,
		Node:       "-",
		Age:        age,
		Status:     status,
	}
}

func (rs *ResourceService) convertReplicaSetToResource(replicaSet appsv1.ReplicaSet) models.Resource {
	replicas := int32(0)
	if replicaSet.Spec.Replicas != nil {
		replicas = *replicaSet.Spec.Replicas
	}

	age := time.Since(replicaSet.CreationTimestamp.Time)

	status := "Running"
	if replicaSet.Status.ReadyReplicas != replicas {
		status = "Pending"
	}

	return models.Resource{
		Name:       replicaSet.Name,
		Containers: fmt.Sprintf("%d/%d", replicaSet.Status.ReadyReplicas, replicas),
		CPU:        "0m",
		Memory:     "0Mi",
		Restarts:   0,
		Node:       "-",
		Age:        age,
		Status:     status,
	}
}

func (rs *ResourceService) convertJobToResource(job batchv1.Job) models.Resource {
	age := time.Since(job.CreationTimestamp.Time)

	status := "Running"
	if job.Status.Succeeded > 0 {
		status = "Completed"
	} else if job.Status.Failed > 0 {
		status = "Failed"
	}

	return models.Resource{
		Name:       job.Name,
		Containers: fmt.Sprintf("%d/%d", job.Status.Succeeded, *job.Spec.Completions),
		CPU:        "0m",
		Memory:     "0Mi",
		Restarts:   0,
		Node:       "-",
		Age:        age,
		Status:     status,
	}
}

func (rs *ResourceService) convertCronJobToResource(cronJob batchv1.CronJob) models.Resource {
	age := time.Since(cronJob.CreationTimestamp.Time)

	status := "Active"
	if cronJob.Spec.Suspend != nil && *cronJob.Spec.Suspend {
		status = "Suspended"
	}

	return models.Resource{
		Name:       cronJob.Name,
		Containers: cronJob.Spec.Schedule,
		CPU:        "-",
		Memory:     "-",
		Restarts:   0,
		Node:       "-",
		Age:        age,
		Status:     status,
	}
}
